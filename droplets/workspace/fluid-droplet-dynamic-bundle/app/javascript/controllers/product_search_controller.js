import { Controller } from "@hotwired/stimulus"

// Product Search Controller - Hybrid search with initial load + API filtering
// Based on Avalara search functionality, adapted for Stimulus
export default class extends Controller {
  static targets = [
    "searchInput",
    "searchButton", 
    "productsContainer",
    "productsList",
    "productsLoading",
    "productsEmpty",
    "productsCount",
    "productsCountNumber"
  ]

  static values = {
    searchUrl: String,
    perPage: { type: Number, default: 50 },
    debounceDelay: { type: Number, default: 300 },
    minSearchLength: { type: Number, default: 2 }
  }

  connect() {
    console.log("🔍 Product Search Controller connected!")

    // Initialize state
    this.isLoading = false
    this.currentProducts = []
    this.currentPage = 1
    this.totalPages = 1
    this.searchTimeout = null
    this.abortController = null // Add abort controller for canceling requests
    this.selectedProducts = new Set()
    this.selectedVariants = new Set()

    // Category limits
    this.categoryLimit = 1
    this.currentlySelected = 0
    this.categoryName = 'Category'

    // Setup checkbox interactions (only once)
    if (!this.checkboxListenersSetup) {
      this.setupCheckboxInteractions()
      this.checkboxListenersSetup = true
    }

    // Setup modal event listeners (only once)
    if (!this.modalListenersSetup) {
      this.setupModalEventListeners()
      this.modalListenersSetup = true
    }

    // Load category data from modal
    this.loadCategoryData()

    // Load initial products after a short delay to ensure DOM is ready
    setTimeout(() => {
      this.loadProducts('', 1)
    }, 100)
  }

  disconnect() {
    // Clear any pending timeouts
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout)
    }

    // Reset flags so listeners can be setup again if reconnected
    this.checkboxListenersSetup = false
    this.modalListenersSetup = false
  }

  // Setup modal event listeners
  setupModalEventListeners() {
    // Listen for modal shown event to reload category data and reset selections
    this.element.addEventListener('shown.bs.modal', () => {
      console.log('🔄 Modal shown - reloading category data and resetting selections')
      this.loadCategoryData()
      // Also clear any existing product displays and reload
      setTimeout(() => {
        this.loadProducts('', 1)
      }, 100)
    })
  }

  // Load category data from modal attributes
  loadCategoryData() {
    const modal = this.element
    if (modal) {
      this.categoryLimit = parseInt(modal.dataset.selectionLimit) || 1
      this.currentlySelected = parseInt(modal.dataset.currentlySelected) || 0
      this.categoryName = modal.dataset.categoryName || 'Category'

      // RESET selections each time modal opens
      this.selectedProducts = new Set()
      this.selectedVariants = new Set()

      console.log(`📊 Category data loaded: ${this.categoryName}, Limit: ${this.categoryLimit}, Current: ${this.currentlySelected}`)
      console.log(`🔄 Selections reset - selectedProducts: ${this.selectedProducts.size}, selectedVariants: ${this.selectedVariants.size}`)

      // Update initial counter
      this.updateSelectionCount()
    }
  }

  // Search button click
  search() {
    const searchTerm = this.searchInputTarget.value.trim()
    this.loadProducts(searchTerm, 1)
  }

  // Search products on input (for real-time search)
  // This method is called from the HTML data-action
  searchProducts(event) {
    // Clear any existing timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout)
    }

    // Get search term from event target or fallback to input target
    const searchTerm = event ? event.target.value.trim() : this.searchInputTarget.value.trim()

    // Immediate search for empty (show initial products)
    if (searchTerm === '') {
      this.loadProducts('', 1)
      return
    }

    // Only search if we have minimum characters
    if (searchTerm.length < this.minSearchLengthValue) {
      console.log(`🔍 Search term "${searchTerm}" too short (min ${this.minSearchLengthValue} chars), waiting...`)
      // Clear current results and show a "type more" message
      this.showTypingMessage(searchTerm.length, this.minSearchLengthValue)
      return
    }

    // Debounced search for terms with minimum length
    this.searchTimeout = setTimeout(() => {
      console.log(`🔍 Search term "${searchTerm}" meets minimum length, searching...`)
      this.loadProducts(searchTerm, 1)
    }, this.debounceDelayValue)
  }

  // Handle Enter key in search input
  searchKeypress(event) {
    if (event.key === 'Enter') {
      event.preventDefault()
      this.search()
    }
  }

  // Main function to load products from API
  async loadProducts(search = '', page = 1) {
    console.log(`🔍 Loading products: search="${search}", page=${page}`)

    // Cancel any previous request
    if (this.abortController) {
      console.log('🔍 Canceling previous request...')
      this.abortController.abort()
    }

    // Create new abort controller for this request
    this.abortController = new AbortController()

    this.isLoading = true
    this.showLoading()

    // Clear list only for first page
    if (page === 1) {
      this.productsListTarget.innerHTML = ''
    }
    this.hideEmpty()

    const params = new URLSearchParams({
      per_page: this.perPageValue,
      page: page
    })

    if (search.trim()) {
      params.append('search', search.trim())
    }

    const url = `${this.searchUrlValue}?${params}`
    console.log(`🔍 Fetching from: ${url}`)

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        signal: this.abortController.signal // Add abort signal
      })

      console.log(`🔍 Response status: ${response.status}`)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.log('🔍 Response data:', data)

      this.isLoading = false
      this.hideLoading()

      if (data.success && data.products && data.products.length > 0) {
        console.log(`🔍 Found ${data.products.length} products`)
        this.currentProducts = data.products
        this.currentPage = data.pagination?.page || page
        this.totalPages = data.pagination?.total_pages || 1

        this.renderProducts(data.products)
        this.updateProductsCount(data.pagination?.total || data.products.length)

        // Dispatch custom event for successful load
        this.dispatch('productsLoaded', {
          detail: {
            products: data.products,
            pagination: data.pagination,
            search: search
          }
        })
      } else {
        console.log('🔍 No products found or API returned error')
        this.currentProducts = []
        this.showEmpty()
        this.updateProductsCount(0)

        if (search.trim()) {
          console.log(`No products found matching "${search}"`)
        } else {
          console.log('No products found')
        }
      }
    } catch (error) {
      this.isLoading = false
      this.hideLoading()

      // Don't show error for aborted requests
      if (error.name === 'AbortError') {
        console.log('🔍 Request was aborted (this is normal when typing fast)')
        return
      }

      console.error('🔍 Error loading products:', error)
      this.showEmpty()
      this.updateProductsCount(0)
    }
  }

  // Render products as draggable cards
  renderProducts(products) {
    const html = products.map(product => this.createProductCard(product)).join('')
    this.productsListTarget.innerHTML = html
  }

  // Create individual product card HTML - Modern cards for wizard modal
  createProductCard(product) {
    const hasVariants = product.variants && product.variants.length > 0
    const variantsCount = hasVariants ? product.variants.length : 0

    // Create variants HTML with modern checkboxes
    const variantsHtml = hasVariants ? `
      <div class="variants-header">
        <h5 class="variants-title">Variants</h5>
        <span class="variants-count">${variantsCount}</span>
      </div>

      <div class="variants-list">
        ${product.variants.map(variant => `
          <div class="variant-item" data-variant-id="${variant.id}">
            <div class="variant-info">
              <div class="variant-title">${variant.title || variant.name || 'Variant'}</div>
              <div class="variant-details">
                <span class="variant-price">$${variant.price || '0.00'}</span>
                <span class="variant-stock">
                  ${variant.inventory_quantity > 0 ?
                    `✅ ${variant.inventory_quantity} in stock` :
                    '❌ Out of stock'
                  }
                </span>
              </div>
            </div>

            <div class="variant-actions">
              <input type="checkbox"
                     class="modern-checkbox variant-checkbox-input"
                     data-variant-id="${variant.id}"
                     data-product-id="${product.id}"
                     id="modal-variant-${variant.id}">
            </div>
          </div>
        `).join('')}
      </div>
    ` : ''

    return `
      <div class="modern-product-card" data-product-id="${product.id}">
        <!-- Single Line Product Info -->
        <div class="product-main-line">
          <input type="checkbox"
                 class="modern-checkbox product-checkbox-input"
                 data-product-id="${product.id}"
                 data-product-sku="${product.sku || ''}"
                 data-product-price="${product.price || 0}"
                 id="modal-product-${product.id}">

          <div class="product-image-container">
            ${product.image_url ?
              `<img src="${product.image_url}" alt="${product.title || product.name}" loading="lazy">` :
              `<div class="product-image-placeholder">${this.getProductEmoji(product)}</div>`
            }
          </div>

          <div class="product-info">
            <span class="product-title">${product.title || product.name || 'Unnamed Product'}</span>
          </div>

          <div class="product-status-price">
            <span class="product-price">$${product.price || '0.00'}</span>
            <span class="status-badge in-stock">✅ AVAILABLE</span>
            ${hasVariants ? `
              <button type="button"
                      class="variants-inline-btn"
                      data-action="click->product-search#toggleVariants"
                      data-product-id="${product.id}"
                      title="Show/hide variants">
                <span class="variants-label">VARIANTS</span>
                <span class="chevron-icon">▼</span>
              </button>
            ` : ''}
          </div>
        </div>

        ${hasVariants ? `
          <!-- Variants Container -->
          <div class="product-variants" data-variants-container="${product.id}" style="display: none;">
            ${variantsHtml}
          </div>
        ` : ''}
      </div>
    `
  }



  // Handle drag start for products
  dragStart(event) {
    const productCard = event.currentTarget
    const productData = {
      id: productCard.dataset.productId,
      sku: productCard.dataset.productSku,
      name: productCard.dataset.productName,
      price: productCard.dataset.productPrice
    }

    event.dataTransfer.setData('application/json', JSON.stringify(productData))
    event.dataTransfer.effectAllowed = 'copy'
    
    // Add visual feedback
    productCard.classList.add('opacity-50')
    
    // Dispatch custom event
    this.dispatch('productDragStart', { detail: { product: productData } })
  }

  // Toggle variants visibility
  toggleVariants(event) {
    // Prevent drag when clicking on variants toggle
    event.stopPropagation()
    event.preventDefault()

    const productCard = event.currentTarget
    const productId = productCard.dataset.productId
    const variantsContainer = productCard.querySelector(`[data-product-variants="${productId}"]`)
    const chevronIcon = productCard.querySelector('.chevron-icon')

    if (variantsContainer && chevronIcon) {
      const isVisible = variantsContainer.style.display !== 'none'

      if (isVisible) {
        variantsContainer.style.display = 'none'
        chevronIcon.style.transform = 'rotate(0deg)'
        chevronIcon.textContent = '▶'
      } else {
        variantsContainer.style.display = 'block'
        chevronIcon.style.transform = 'rotate(90deg)'
        chevronIcon.textContent = '▼'
      }
    }
  }

  // UI Helper methods
  showLoading() {
    this.productsLoadingTarget.classList.remove('hidden')
  }

  hideLoading() {
    this.productsLoadingTarget.classList.add('hidden')
  }

  showEmpty() {
    this.productsEmptyTarget.classList.remove('hidden')
  }

  hideEmpty() {
    this.productsEmptyTarget.classList.add('hidden')
  }

  updateProductsCount(count) {
    if (this.hasProductsCountTarget && this.hasProductsCountNumberTarget) {
      this.productsCountNumberTarget.textContent = count
      if (count > 0) {
        this.productsCountTarget.classList.remove('hidden')
      } else {
        this.productsCountTarget.classList.add('hidden')
      }
    }
  }

  // Show message when user is typing but hasn't reached minimum length
  showTypingMessage(currentLength, minLength) {
    const remaining = minLength - currentLength
    const message = `Type ${remaining} more character${remaining > 1 ? 's' : ''} to search...`

    this.productsListTarget.innerHTML = `
      <div class="text-center py-5 text-muted">
        <div class="mb-3" style="font-size: 2rem;">🔍</div>
        <h6 class="mb-2">${message}</h6>
        <p class="mb-2 small">Minimum ${minLength} characters required</p>
        <div class="mt-3">
          <small class="text-muted">
            <strong>Search fields:</strong> Name, SKU, description, variants<br>
            <strong>Examples:</strong> "protein", "SUPP-001", "weight loss"
          </small>
        </div>
      </div>
    `

    this.hideLoading()
    this.hideEmpty()
    this.updateProductsCount(0)
  }

  // Setup checkbox interactions for modern cards
  setupCheckboxInteractions() {
    // Listen for checkbox changes
    document.addEventListener('change', (e) => {
      if (e.target.classList.contains('product-checkbox-input') &&
          e.target.id.startsWith('modal-product-')) {
        this.handleProductCheckbox(e.target)
      }

      if (e.target.classList.contains('variant-checkbox-input') &&
          e.target.id.startsWith('modal-variant-')) {
        this.handleVariantCheckbox(e.target)
      }
    })

    // Listen for card clicks to toggle checkboxes
    document.addEventListener('click', (e) => {
      const card = e.target.closest('.modern-product-card')
      if (card && !e.target.closest('.product-actions') &&
          !e.target.closest('.product-checkbox-container') &&
          !e.target.closest('.variant-actions')) {
        const checkbox = card.querySelector('.product-checkbox-input')
        if (checkbox) {
          checkbox.checked = !checkbox.checked
          this.handleProductCheckbox(checkbox)
        }
      }
    })
  }

  // Handle product checkbox changes - Smart logic with limits
  handleProductCheckbox(checkbox) {
    const productId = checkbox.dataset.productId
    const card = checkbox.closest('.modern-product-card')
    const variantCheckboxes = card.querySelectorAll('.variant-checkbox-input')

    if (checkbox.checked) {
      // Check if we've reached the limit
      const totalSelected = this.selectedProducts.size + this.selectedVariants.size + this.currentlySelected
      if (totalSelected >= this.categoryLimit) {
        // Prevent selection - we've reached the limit
        checkbox.checked = false
        this.showLimitReachedMessage()
        return
      }

      // Select product and ALL its variants
      this.selectedProducts.add(productId)
      card.classList.add('selected')

      // Auto-select all variants
      variantCheckboxes.forEach(variantCheckbox => {
        const variantId = variantCheckbox.dataset.variantId
        const variantItem = variantCheckbox.closest('.variant-item')

        variantCheckbox.checked = true
        this.selectedVariants.add(variantId)
        variantItem.classList.add('selected')
      })

      console.log('✅ Selected product + all variants:', productId)
    } else {
      // Deselect product and ALL its variants
      this.selectedProducts.delete(productId)
      card.classList.remove('selected')

      // Auto-deselect all variants
      variantCheckboxes.forEach(variantCheckbox => {
        const variantId = variantCheckbox.dataset.variantId
        const variantItem = variantCheckbox.closest('.variant-item')

        variantCheckbox.checked = false
        this.selectedVariants.delete(variantId)
        variantItem.classList.remove('selected')
      })

      console.log('❌ Deselected product + all variants:', productId)
    }

    this.updateSelectionCount()
    this.updateCheckboxStates()
  }

  // Handle variant checkbox changes - Smart logic with limits
  handleVariantCheckbox(checkbox) {
    const variantId = checkbox.dataset.variantId
    const productId = checkbox.dataset.productId
    const variantItem = checkbox.closest('.variant-item')
    const card = checkbox.closest('.modern-product-card')
    const productCheckbox = card.querySelector('.product-checkbox-input')
    const allVariantCheckboxes = card.querySelectorAll('.variant-checkbox-input')

    if (checkbox.checked) {
      // Check if we've reached the limit
      const totalSelected = this.selectedProducts.size + this.selectedVariants.size + this.currentlySelected
      if (totalSelected >= this.categoryLimit) {
        // Prevent selection - we've reached the limit
        checkbox.checked = false
        this.showLimitReachedMessage()
        return
      }

      // Select this variant
      this.selectedVariants.add(variantId)
      variantItem.classList.add('selected')

      // Auto-select the parent product
      this.selectedProducts.add(productId)
      productCheckbox.checked = true
      card.classList.add('selected')

      console.log('✅ Selected variant + parent product:', variantId, productId)
    } else {
      // Deselect this variant
      this.selectedVariants.delete(variantId)
      variantItem.classList.remove('selected')

      // Check if any other variants are still selected
      const hasSelectedVariants = Array.from(allVariantCheckboxes).some(cb =>
        cb.checked && cb !== checkbox
      )

      if (!hasSelectedVariants) {
        // No variants selected, deselect the parent product
        this.selectedProducts.delete(productId)
        productCheckbox.checked = false
        card.classList.remove('selected')
        console.log('❌ Deselected variant + parent product (no variants left):', variantId, productId)
      } else {
        console.log('❌ Deselected variant (parent product stays selected):', variantId)
      }
    }

    this.updateSelectionCount()
    this.updateCheckboxStates()
  }

  // Update selection count in modal
  updateSelectionCount() {
    // Count unique products (not variants + products separately)
    const totalSelected = this.selectedProducts.size
    const totalWithExisting = totalSelected + this.currentlySelected
    const isAtLimit = totalWithExisting >= this.categoryLimit
    const isOverLimit = totalWithExisting > this.categoryLimit

    console.log(`📊 Selection updated: ${totalSelected} new + ${this.currentlySelected} existing = ${totalWithExisting}/${this.categoryLimit}`)

    // Update modal title
    const modalTitle = document.getElementById('productSelectionModalLabel')
    if (modalTitle) {
      modalTitle.textContent = `Add Products to Category: ${this.categoryName} (${totalWithExisting}/${this.categoryLimit} selected)`
    }

    // Update modal footer counter
    const counter = document.getElementById('selection-counter')
    const addBtn = document.getElementById('add-selected-btn')

    if (counter) {
      const badge = counter.querySelector('.badge')
      if (badge) {
        let badgeClass = 'badge bg-secondary'
        let badgeText = `${totalSelected} selected`

        if (isOverLimit) {
          badgeClass = 'badge bg-danger'
          badgeText = `${totalSelected} selected (${totalWithExisting - this.categoryLimit} over limit)`
        } else if (isAtLimit) {
          badgeClass = 'badge bg-warning'
          badgeText = `${totalSelected} selected (limit reached)`
        } else if (totalSelected > 0) {
          badgeClass = 'badge bg-success'
          badgeText = `${totalSelected} selected`
        }

        badge.textContent = badgeText
        badge.className = badgeClass
      }
    }

    if (addBtn) {
      addBtn.disabled = totalSelected === 0 || isOverLimit

      if (isOverLimit) {
        addBtn.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>Over Limit (${totalWithExisting}/${this.categoryLimit})`
      } else if (isAtLimit) {
        addBtn.innerHTML = `<i class="fas fa-check me-1"></i>Add ${totalSelected} Selected (Full)`
      } else if (totalSelected > 0) {
        addBtn.innerHTML = `<i class="fas fa-plus me-1"></i>Add ${totalSelected} Selected`
      } else {
        addBtn.innerHTML = `<i class="fas fa-plus me-1"></i>Add Selected Products`
      }
    }

    // Dispatch event for other controllers to listen
    this.dispatch('selectionChanged', {
      detail: {
        selectedProducts: Array.from(this.selectedProducts),
        selectedVariants: Array.from(this.selectedVariants),
        totalSelected: totalSelected,
        totalWithExisting: totalWithExisting,
        categoryLimit: this.categoryLimit,
        isAtLimit: isAtLimit,
        isOverLimit: isOverLimit
      }
    })
  }

  // Confirm selection (called when Add Selected button is clicked)
  confirmSelection(event) {
    console.log('🎯 PRODUCT SEARCH: confirmSelection called!')

    // Call the category builder's addSelectedProducts method
    const categoryBuilder = window.categoryBuilderController
    if (categoryBuilder && categoryBuilder.addSelectedProducts) {
      console.log('🔗 Calling category builder addSelectedProducts...')
      categoryBuilder.addSelectedProducts(event)
    } else {
      console.error('❌ Category builder controller not found!')
    }
  }

  // Update checkbox states based on limits
  updateCheckboxStates() {
    const totalSelected = this.selectedProducts.size + this.selectedVariants.size + this.currentlySelected
    const isAtLimit = totalSelected >= this.categoryLimit

    // Get all unselected checkboxes
    const unselectedProductCheckboxes = document.querySelectorAll('.product-checkbox-input:not(:checked)')
    const unselectedVariantCheckboxes = document.querySelectorAll('.variant-checkbox-input:not(:checked)')

    // Disable/enable checkboxes based on limit
    unselectedProductCheckboxes.forEach(checkbox => {
      checkbox.disabled = isAtLimit
      const card = checkbox.closest('.modern-product-card')
      if (card) {
        card.classList.toggle('limit-reached', isAtLimit)
      }
    })

    unselectedVariantCheckboxes.forEach(checkbox => {
      checkbox.disabled = isAtLimit
      const variantItem = checkbox.closest('.variant-item')
      if (variantItem) {
        variantItem.classList.toggle('limit-reached', isAtLimit)
      }
    })

    console.log(`🔒 Checkboxes ${isAtLimit ? 'disabled' : 'enabled'} - limit ${isAtLimit ? 'reached' : 'not reached'}`)
  }

  // Show limit reached message
  showLimitReachedMessage() {
    // Create temporary toast message
    const toast = document.createElement('div')
    toast.className = 'alert alert-warning alert-dismissible fade show position-fixed'
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;'
    toast.innerHTML = `
      <strong>Category Limit Reached!</strong><br>
      This category can only have ${this.categoryLimit} product${this.categoryLimit > 1 ? 's' : ''}.
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `

    document.body.appendChild(toast)

    // Auto-remove after 4 seconds
    setTimeout(() => {
      if (toast.parentNode) {
        toast.remove()
      }
    }, 4000)

    console.log(`🚫 Limit reached message shown: ${this.categoryLimit} products max`)
  }

  // Get selected items for form submission
  getSelectedItems() {
    return {
      products: Array.from(this.selectedProducts),
      variants: Array.from(this.selectedVariants),
      total: this.selectedProducts.size + this.selectedVariants.size,
      totalWithExisting: this.selectedProducts.size + this.selectedVariants.size + this.currentlySelected,
      categoryLimit: this.categoryLimit,
      isAtLimit: (this.selectedProducts.size + this.selectedVariants.size + this.currentlySelected) >= this.categoryLimit
    }
  }

  // Toggle variants visibility
  toggleVariants(event) {
    event.preventDefault()
    event.stopPropagation()

    const button = event.currentTarget
    const productId = button.dataset.productId
    const variantsContainer = document.querySelector(`[data-variants-container="${productId}"]`)
    const chevronIcon = button.querySelector('.chevron-icon')

    if (variantsContainer && chevronIcon) {
      const isVisible = variantsContainer.style.display !== 'none'

      if (isVisible) {
        // Hide variants
        variantsContainer.style.display = 'none'
        chevronIcon.textContent = '▼'
        button.classList.remove('expanded')
        console.log('🔽 Collapsed variants for product:', productId)
      } else {
        // Show variants
        variantsContainer.style.display = 'block'
        chevronIcon.textContent = '▲'
        button.classList.add('expanded')
        console.log('🔼 Expanded variants for product:', productId)
      }
    }
  }

  // Get emoji for product type
  getProductEmoji(product) {
    const name = (product.title || product.name || '').toLowerCase()

    if (name.includes('protein') || name.includes('powder')) return '🥤'
    if (name.includes('vitamin') || name.includes('supplement')) return '💊'
    if (name.includes('energy') || name.includes('drink')) return '⚡'
    if (name.includes('bar') || name.includes('snack')) return '🍫'
    if (name.includes('oil') || name.includes('omega')) return '🫒'
    if (name.includes('probiotic') || name.includes('digestive')) return '🦠'
    if (name.includes('weight') || name.includes('loss')) return '⚖️'
    if (name.includes('beauty') || name.includes('skin')) return '✨'

    return '📦' // Default product icon
  }
}
